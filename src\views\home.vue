<template>
  <div class="dashboard-container">
    <div class="echart-container">
      <div id="chart-panel" :style="{ height: viewportHeight, width: width }" />

    </div>

  </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import echarts from 'echarts'
import { queryDeviceSwitch } from '@/api/fhm/deviceShowApi'

require('echarts/theme/macarons') // echarts theme
import request from '@/utils/request'
import fhm from './dashboard/fhm'
import resize from './dashboard/mixins/resize'

export default {
  name: 'Dashboard',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    fhm

  },
  mixins: [resize],
  data() {
    return {

      arrDevice: [],
      viewportHeight: window.innerHeight, // 初始设置为当前窗口高度
      width: '100%',
      height: '800px',
      // 颜色
      colors: [
        '#3CB371'
      ]
    }
  },
  mounted() {
    this.updateViewportHeight()
    window.addEventListener('resize', this.updateViewportHeight)
    this.$nextTick(() => {
      this.getData()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateViewportHeight)
  },
  methods: {

    updateViewportHeight() {
      this.viewportHeight = window.innerHeight - 100 + 'px'
    },
    /*      getData() {
           const params = {};
           const res =  queryDeviceSwitch(params);
           let arr = [];
           arr.push(res)
           arr = this.handleData(arr, 0)
           this.arrDevice = arr;

           return arr;
         },*/

    getData() {
      const params = {

      }
      let arr = []
      // 使用request调用API，但不显示loading状态
      request({
        url: 'api/deviceApi/queryDeviceSwitch',
        method: 'get',
        params: params
      }).then(res => {
        debugger
        arr.push(res)
        arr = this.handleData(arr, 0)

        this.arrDevice = arr
        this.initChart(arr)
      }).catch(error => {
        console.error('静默获取历史数据失败:', error)
        // 静默失败，不显示错误消息
      })
      return arr
    },

    // 获取所有数据
    /*   getData() {
            const data = {
              name: '升压站',
              value: "1",
              id: 0,
              children: []
            }
            for (let i = 1; i <= 19; i++) {
              const obj = {
                name: i + '#箱变',
                value: i+"swewe",
                id: 0,
                children: []
              }
              for (let j = 1; j <= 8; j++) {
                const obj2 = {
                  id: 0,
                  name: `逆变器-${i}-${j}`,
                  value: 1 + '-' + i + '-' + j
                }
                // eslint-disable-next-line eqeqeq
                if (j % 2 == 1) {
                  obj2.children = []
                  for (let k = 1; k <= 2; k++) {
                    const obj3 = {
                      id: 0,
                      name: `组串-${i}-${j}-${k}`,
                      value: 1 + '-' + i + '-' + j + '-' + k
                    }
                    obj2.children.push(obj3)
                  }
                }

                obj.children.push(obj2)
              }

              data.children.push(obj)
            }
            let arr = []
            arr.push(data)
            console.log(JSON.stringify(arr))

            arr = this.handleData(arr, 0)

            return arr
          },*/

    // 数据颜色  组装
    handleData(data, index, color = '#3CB371') {
      // index标识第几层
      return data.map((item, index2) => {
        // 计算出颜色
        // eslint-disable-next-line eqeqeq
        if (index == 1) {
          // eslint-disable-next-line no-undef,eqeqeq
          //  color = this.colors.find((item, eq) => eq == index2 % 5)
          color = '#3CB371'
        }
        // 设置节点大小
        if (index === 0 || index === 1) {
          item.label = {
            position: 'inside'
            //   rotate: 0,
            //   borderRadius: "50%",
          }
        }
        // 设置label大小 - 矩形布局使用[宽, 高]格式
        switch (index) {
          case 0:
            item.symbolSize = [80, 40] // 根节点使用较大的矩形
            break
          case 1:
            item.symbolSize = [60, 30] // 二级节点
            break
          default:
            item.symbolSize = [40, 20] // 叶子节点
            break
        }
        // 设置线条颜色
        item.lineStyle = {
          color: color
        }

        if (item.children) { // 存在子节点
          item.itemStyle = {
            borderColor: '#3CB371',
            color: '#3CB371'
          }

          if (index2 == 1) {
            item.children = this.handleData(item.children, index + 1, 'red')
          } else {
            item.children = this.handleData(item.children, index + 1, color)
          }
        } else { // 不存在
          item.itemStyle = {
            color: '#3CB371',
            borderColor: '#3CB371'
          }
        }
        return item
      })
    },

    initChart(data) {
      // this.chart = echarts.init(this.$el, 'macarons')
      var dom = document.getElementById('chart-panel')
      this.chart = echarts.init(dom)

      debugger

      const option = {
        type: 'tree',
        backgroundColor: '#000',
        toolbox: { // 工具栏
          show: true,
          iconStyle: {
            borderColor: '#03ceda'
          },
          feature: {
            restore: {}
          }
        },
        tooltip: { // 提示框
          trigger: 'item',
          triggerOn: 'mousemove',
          backgroundColor: 'rgba(1,70,86,1)',
          borderColor: 'rgba(0,246,255,1)',
          borderWidth: 0.5,
          textStyle: {
            fontSize: 10
          }
        },
        /*
                  symbol: 'rect', // 节点标记形状
                  edgeShape: 'polyline', //设置连接线曲线还是折线，默认情况下是曲线，curve曲线 polyline直线
                  orient: 'vertical', //树整体的方向horizontal横向 vertical竖向*/
        series: [{
          type: 'tree',
          hoverAnimation: true, // hover样式
          data: data,
          top: 100,
          bottom: 200,
          left: 100,
          right: 100,
          layout: 'orthogonal', // 改为正交布局（直角布局）
          orient: 'TB', // 从上到下的方向，也可以用 'LR'（从左到右）、'RL'（从右到左）、'BT'（从下到上）
          symbol: 'rect', // 改为矩形节点
          symbolSize: [20, 15], // 设置矩形节点的宽高
          nodePadding: 20,
          animationDurationUpdate: 750,
          expandAndCollapse: true, // 子树折叠和展开的交互，默认打开
          initialTreeDepth: 2,
          roam: true, // 是否开启鼠标缩放和平移漫游。scale/move/true
          focusNodeAdjacency: true,
          itemStyle: {
            borderWidth: 1,
            borderRadius: 3 // 添加圆角让矩形更美观
          },
          label: { // 标签样式
            color: '#fff',
            fontSize: 10,
            fontFamily: 'SourceHanSansCN'
            // position: "top",
            // rotate: 0,
          },
          lineStyle: {
            width: 1,
            curveness: 0 // 设置为0，使连接线为直线
          }
        }]
      }
      this.chart.setOption(option)
      /* if (option && typeof option === 'object') {
         myChart.setOption(option)
       }*/
    }

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped></style>
